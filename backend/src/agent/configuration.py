import os
from pydantic import BaseModel, <PERSON>
from typing import Any, Optional

from langchain_core.runnables import RunnableConfig


class Configuration(BaseModel):
    """The configuration for the agent."""

    query_generator_model: str = Field(
        default="gemini-2.0-flash",
        metadata={
            "description": "The name of the language model to use for the agent's query generation."
        },
    )

    reflection_model: str = Field(
        default="gemini-2.5-flash",
        metadata={
            "description": "The name of the language model to use for the agent's reflection."
        },
    )

    answer_model: str = Field(
        default="gemini-2.5-pro",
        metadata={
            "description": "The name of the language model to use for the agent's answer."
        },
    )

    number_of_initial_queries: int = Field(
        default=3,
        metadata={"description": "The number of initial search queries to generate."},
    )

    max_research_loops: int = Field(
        default=2,
        metadata={"description": "The maximum number of research loops to perform."},
    )

    # Credit rating specific configuration
    enable_credit_rating_mode: bool = Field(
        default=True,
        metadata={"description": "Enable automatic detection and handling of credit rating requests."},
    )

    credit_rating_time_range_start: int = Field(
        default=2019,
        metadata={"description": "Start year for credit rating data collection."},
    )

    credit_rating_time_range_end: int = Field(
        default=2024,
        metadata={"description": "End year for credit rating data collection."},
    )

    credit_rating_primary_query_count: int = Field(
        default=4,
        metadata={"description": "Number of primary queries to generate for direct power plant ratings."},
    )

    credit_rating_fallback_query_count: int = Field(
        default=4,
        metadata={"description": "Number of fallback queries to generate for parent company ratings."},
    )

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """Create a Configuration instance from a RunnableConfig."""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )

        # Get raw values from environment or config
        raw_values: dict[str, Any] = {
            name: os.environ.get(name.upper(), configurable.get(name))
            for name in cls.model_fields.keys()
        }

        # Filter out None values
        values = {k: v for k, v in raw_values.items() if v is not None}

        return cls(**values)
