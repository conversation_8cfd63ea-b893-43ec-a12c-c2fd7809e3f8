import os
import re
import logging
from typing import Dict, Any, Optional

from agent.tools_and_schemas import (
    CreditRatingQueryList,
    CreditRatingReflection,
    CreditRatingResponse
)
from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from langgraph.types import Send
from langchain_core.runnables import RunnableConfig
from google.genai import Client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from agent.state import (
    OverallState,
    CreditRatingQueryGenerationState,
    CreditRatingReflectionState,
    WebSearchState,
)
from agent.configuration import Configuration
from agent.prompts import (
    get_current_date,
    credit_rating_query_generator_instructions,
    credit_rating_web_searcher_instructions,
    credit_rating_reflection_instructions,
    credit_rating_answer_instructions,
)
from langchain_google_genai import ChatGoogleGenerativeAI
from agent.utils import (
    get_citations,
    get_research_topic,
    insert_citation_markers,
    resolve_urls,
)

load_dotenv()

if os.getenv("GEMINI_API_KEY") is None:
    raise ValueError("GEMINI_API_KEY is not set")

# Used for Google Search API
genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))


def extract_power_plant_info(research_topic: str) -> Dict[str, str]:
    """Extract power plant name and geographic region from research topic."""
    # Simple extraction logic - can be enhanced with NLP
    power_plant_name = ""
    geographic_region = ""
    
    # Look for common power plant keywords
    power_plant_keywords = ["power plant", "thermal power", "nuclear power", "solar power", 
                           "wind power", "hydroelectric", "power project", "power station"]
    
    # Look for geographic indicators
    geographic_keywords = {
        "India": ["India", "Indian", "Maharashtra", "Gujarat", "Tamil Nadu", "Karnataka", 
                 "Andhra Pradesh", "Telangana", "Rajasthan", "Uttar Pradesh"],
        "United States": ["USA", "US", "United States", "America", "California", "Texas", 
                         "Florida", "New York", "Pennsylvania"],
        "United Kingdom": ["UK", "United Kingdom", "Britain", "England", "Scotland", "Wales"],
        "Europe": ["Germany", "France", "Spain", "Italy", "Netherlands", "Belgium", "Austria"]
    }
    
    # Extract power plant name (simplified approach)
    topic_lower = research_topic.lower()
    for keyword in power_plant_keywords:
        if keyword in topic_lower:
            # Try to extract the name before the keyword
            parts = research_topic.split()
            for i, part in enumerate(parts):
                if keyword.split()[0] in part.lower():
                    # Take words before this as potential plant name
                    power_plant_name = " ".join(parts[:i+len(keyword.split())])
                    break
            break
    
    if not power_plant_name:
        # Fallback: use first few words
        words = research_topic.split()
        power_plant_name = " ".join(words[:4]) if len(words) >= 4 else research_topic
    
    # Extract geographic region
    for region, keywords in geographic_keywords.items():
        for keyword in keywords:
            if keyword.lower() in topic_lower:
                geographic_region = region
                break
        if geographic_region:
            break
    
    if not geographic_region:
        geographic_region = "Unknown"
    
    return {
        "power_plant_name": power_plant_name.strip(),
        "geographic_region": geographic_region
    }


def generate_credit_rating_query(state: OverallState, config: RunnableConfig) -> CreditRatingQueryGenerationState:
    """LangGraph node that generates credit rating specific search queries.

    Uses Gemini 2.0 Flash to create optimized search queries for power plant credit rating research
    using a two-tier search strategy.

    Args:
        state: Current graph state containing the User's question
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including credit rating specific queries
    """
    try:
        logger.info("Starting credit rating query generation")
        configurable = Configuration.from_runnable_config(config)

        # Extract power plant information from research topic
        research_topic = get_research_topic(state["messages"])
        logger.info(f"Research topic: {research_topic}")

        plant_info = extract_power_plant_info(research_topic)
        logger.info(f"Extracted plant info: {plant_info}")

        # Validate extracted information
        if not plant_info["power_plant_name"]:
            logger.warning("Could not extract power plant name, using research topic")
            plant_info["power_plant_name"] = research_topic[:50] + "..." if len(research_topic) > 50 else research_topic

        # Update state with extracted information
        state["power_plant_name"] = plant_info["power_plant_name"]
        state["geographic_region"] = plant_info["geographic_region"]
        state["search_mode"] = "credit_rating"
        state["credit_rating_strategy"] = "primary"

        # init Gemini 2.0 Flash
        llm = ChatGoogleGenerativeAI(
            model=configurable.query_generator_model,
            temperature=1.0,
            max_retries=2,
            api_key=os.getenv("GEMINI_API_KEY"),
        )
        structured_llm = llm.with_structured_output(CreditRatingQueryList)

        # Format the prompt
        current_date = get_current_date()
        formatted_prompt = credit_rating_query_generator_instructions.format(
            current_date=current_date,
            research_topic=research_topic,
            power_plant_name=plant_info["power_plant_name"],
            geographic_region=plant_info["geographic_region"],
        )

        # Generate the search queries
        logger.info("Generating credit rating queries with LLM")
        result = structured_llm.invoke(formatted_prompt)

        logger.info(f"Generated {len(result.primary_queries)} primary queries and {len(result.fallback_queries)} fallback queries")

        return {
            "primary_queries": result.primary_queries,
            "fallback_queries": result.fallback_queries,
            "rationale": result.rationale,
            "power_plant_name": result.power_plant_name,
            "geographic_region": result.geographic_region,
        }

    except Exception as e:
        logger.error(f"Error in credit rating query generation: {str(e)}")
        # Return fallback queries based on research topic
        fallback_queries = [
            f"{research_topic} credit rating",
            f"{research_topic} financial rating",
            f"{research_topic} bond rating"
        ]
        return {
            "primary_queries": fallback_queries,
            "fallback_queries": [],
            "rationale": f"Fallback queries generated due to error: {str(e)}",
            "power_plant_name": research_topic,
            "geographic_region": "Unknown",
        }


def continue_to_credit_rating_web_research(state: CreditRatingQueryGenerationState):
    """LangGraph node that sends credit rating queries to web research.

    This spawns web research nodes for primary queries first.
    """
    # Start with primary queries
    queries_to_use = state["primary_queries"]
    
    return [
        Send("credit_rating_web_research", {
            "search_query": search_query, 
            "id": int(idx),
            "power_plant_name": state["power_plant_name"],
            "geographic_region": state["geographic_region"]
        })
        for idx, search_query in enumerate(queries_to_use)
    ]


def credit_rating_web_research(state: WebSearchState, config: RunnableConfig) -> OverallState:
    """LangGraph node that performs credit rating specific web research.

    Executes web search using Google Search API with credit rating specific instructions.

    Args:
        state: Current graph state containing the search query and context
        config: Configuration for the runnable, including search API settings

    Returns:
        Dictionary with state update, including sources and research results
    """
    try:
        logger.info(f"Starting credit rating web research for query: {state['search_query']}")

        # Configure
        configurable = Configuration.from_runnable_config(config)

        # Get power plant context from state
        power_plant_name = state.get("power_plant_name", "Unknown Power Plant")
        geographic_region = state.get("geographic_region", "Unknown Region")

        logger.info(f"Research context - Plant: {power_plant_name}, Region: {geographic_region}")

        formatted_prompt = credit_rating_web_searcher_instructions.format(
            current_date=get_current_date(),
            search_query=state["search_query"],
            power_plant_name=power_plant_name,
            geographic_region=geographic_region,
        )

        # Uses the google genai client as the langchain client doesn't return grounding metadata
        logger.info("Executing Google Search API call")
        response = genai_client.models.generate_content(
            model=configurable.query_generator_model,
            contents=formatted_prompt,
            config={
                "tools": [{"google_search": {}}],
                "temperature": 0,
            },
        )

        # Validate response
        if not response or not response.candidates:
            logger.warning("No response candidates from Google Search API")
            return {
                "sources_gathered": [],
                "search_query": [state["search_query"]],
                "web_research_result": ["No search results found for this query."],
            }

        candidate = response.candidates[0]
        if not hasattr(candidate, 'grounding_metadata') or not candidate.grounding_metadata:
            logger.warning("No grounding metadata in response")
            return {
                "sources_gathered": [],
                "search_query": [state["search_query"]],
                "web_research_result": [response.text if response.text else "No detailed results found."],
            }

        # resolve the urls to short urls for saving tokens and time
        resolved_urls = resolve_urls(
            candidate.grounding_metadata.grounding_chunks, state["id"]
        )

        # Gets the citations and adds them to the generated text
        citations = get_citations(response, resolved_urls)
        modified_text = insert_citation_markers(response.text, citations)
        sources_gathered = [item for citation in citations for item in citation["segments"]]

        logger.info(f"Found {len(sources_gathered)} sources for credit rating research")

        return {
            "sources_gathered": sources_gathered,
            "search_query": [state["search_query"]],
            "web_research_result": [modified_text],
        }

    except Exception as e:
        logger.error(f"Error in credit rating web research: {str(e)}")
        return {
            "sources_gathered": [],
            "search_query": [state["search_query"]],
            "web_research_result": [f"Error occurred during search: {str(e)}"],
        }


def credit_rating_reflection(state: OverallState, config: RunnableConfig) -> CreditRatingReflectionState:
    """LangGraph node that analyzes credit rating research completeness.

    Analyzes the current research results to identify gaps in credit rating coverage
    and generates potential follow-up queries.

    Args:
        state: Current graph state containing research results and context
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including reflection analysis
    """
    configurable = Configuration.from_runnable_config(config)
    
    # Increment the research loop count and get the reasoning model
    state["research_loop_count"] = state.get("research_loop_count", 0) + 1
    reasoning_model = state.get("reasoning_model", configurable.reflection_model)

    # Format the prompt
    current_date = get_current_date()
    power_plant_name = state.get("power_plant_name", "Unknown Power Plant")
    geographic_region = state.get("geographic_region", "Unknown Region")
    
    formatted_prompt = credit_rating_reflection_instructions.format(
        current_date=current_date,
        power_plant_name=power_plant_name,
        geographic_region=geographic_region,
        summaries="\n\n---\n\n".join(state["web_research_result"]),
    )
    
    # init Reasoning Model
    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=1.0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    result = llm.with_structured_output(CreditRatingReflection).invoke(formatted_prompt)

    return {
        "is_sufficient": result.is_sufficient,
        "knowledge_gap": result.knowledge_gap,
        "follow_up_queries": result.follow_up_queries,
        "research_loop_count": state["research_loop_count"],
        "number_of_ran_queries": len(state["search_query"]),
        "rating_completeness": result.rating_completeness,
        "suggested_strategy": result.suggested_strategy,
    }


def finalize_credit_rating_answer(state: OverallState, config: RunnableConfig):
    """LangGraph node that finalizes the credit rating analysis.

    Processes research results and generates structured credit rating output.

    Args:
        state: Current graph state containing research results
        config: Configuration for the runnable

    Returns:
        Dictionary with state update, including structured credit rating response
    """
    configurable = Configuration.from_runnable_config(config)
    reasoning_model = state.get("reasoning_model") or configurable.answer_model

    # Format the prompt
    current_date = get_current_date()
    power_plant_name = state.get("power_plant_name", "Unknown Power Plant")
    geographic_region = state.get("geographic_region", "Unknown Region")
    
    formatted_prompt = credit_rating_answer_instructions.format(
        current_date=current_date,
        power_plant_name=power_plant_name,
        geographic_region=geographic_region,
        summaries="\n---\n\n".join(state["web_research_result"]),
    )

    # init Reasoning Model
    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    result = llm.invoke(formatted_prompt)

    # Try to extract JSON from the response
    try:
        # Look for JSON in the response
        import json

        # Try multiple JSON extraction patterns
        json_patterns = [
            r'```json\s*(\{.*?\})\s*```',  # Standard JSON code block
            r'```\s*(\{.*?\})\s*```',      # JSON without language specifier
            r'(\{[^{}]*"credit_rating"[^{}]*\{.*?\}[^{}]*\})',  # Direct JSON pattern
        ]

        credit_rating_data = None
        for pattern in json_patterns:
            json_match = re.search(pattern, result.content, re.DOTALL)
            if json_match:
                try:
                    credit_rating_data = json.loads(json_match.group(1))
                    logger.info("Successfully extracted JSON from response")
                    break
                except json.JSONDecodeError:
                    continue

        if credit_rating_data:
            # Always store the data first (even if validation fails)
            state["credit_rating_result"] = credit_rating_data
            logger.info(f"Stored credit rating data with {len(credit_rating_data.get('credit_rating', []))} agencies")

            # Try to validate the extracted data
            try:
                validated_data = CreditRatingResponse(**credit_rating_data)
                state["credit_rating_result"] = validated_data.model_dump()
                logger.info(f"Successfully validated credit rating data")
            except Exception as validation_error:
                logger.warning(f"JSON validation failed: {validation_error}")
                # Keep the unvalidated data but add validation error info
                state["credit_rating_result"] = {
                    **credit_rating_data,
                    "_validation_error": str(validation_error),
                    "_validation_status": "failed"
                }
                logger.info(f"Stored unvalidated credit rating data for manual review")
        else:
            logger.warning("No JSON found in response")
            state["credit_rating_result"] = {"error": "No JSON found in response", "raw_response": result.content}

    except Exception as e:
        logger.error(f"Error extracting JSON: {e}")
        # If JSON extraction fails, store the raw response
        state["credit_rating_result"] = {"error": f"Failed to parse credit rating data: {str(e)}", "raw_response": result.content}

    # Replace the short urls with the original urls and add all used urls to the sources_gathered
    unique_sources = []
    for source in state["sources_gathered"]:
        if source["short_url"] in result.content:
            result.content = result.content.replace(
                source["short_url"], source["value"]
            )
            unique_sources.append(source)

    return {
        "messages": [AIMessage(content=result.content)],
        "sources_gathered": unique_sources,
    }
