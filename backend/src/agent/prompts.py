from datetime import datetime


# Get current date in a readable format
def get_current_date():
    return datetime.now().strftime("%B %d, %Y")


query_writer_instructions = """Your goal is to generate sophisticated and diverse web search queries. These queries are intended for an advanced automated web research tool capable of analyzing complex results, following links, and synthesizing information.

Instructions:
- Always prefer a single search query, only add another query if the original question requests multiple aspects or elements and one query is not enough.
- Each query should focus on one specific aspect of the original question.
- Don't produce more than {number_queries} queries.
- Queries should be diverse, if the topic is broad, generate more than 1 query.
- Don't generate multiple similar queries, 1 is enough.
- Query should ensure that the most current information is gathered. The current date is {current_date}.

Format: 
- Format your response as a JSON object with ALL two of these exact keys:
   - "rationale": Brief explanation of why these queries are relevant
   - "query": A list of search queries

Example:

Topic: What revenue grew more last year apple stock or the number of people buying an iphone
```json
{{
    "rationale": "To answer this comparative growth question accurately, we need specific data points on Apple's stock performance and iPhone sales metrics. These queries target the precise financial information needed: company revenue trends, product-specific unit sales figures, and stock price movement over the same fiscal period for direct comparison.",
    "query": ["Apple total revenue growth fiscal year 2024", "iPhone unit sales growth fiscal year 2024", "Apple stock price growth fiscal year 2024"],
}}
```

Context: {research_topic}"""


web_searcher_instructions = """Conduct targeted Google Searches to gather the most recent, credible information on "{research_topic}" and synthesize it into a verifiable text artifact.

Instructions:
- Query should ensure that the most current information is gathered. The current date is {current_date}.
- Conduct multiple, diverse searches to gather comprehensive information.
- Consolidate key findings while meticulously tracking the source(s) for each specific piece of information.
- The output should be a well-written summary or report based on your search findings. 
- Only include the information found in the search results, don't make up any information.

Research Topic:
{research_topic}
"""

reflection_instructions = """You are an expert research assistant analyzing summaries about "{research_topic}".

Instructions:
- Identify knowledge gaps or areas that need deeper exploration and generate a follow-up query. (1 or multiple).
- If provided summaries are sufficient to answer the user's question, don't generate a follow-up query.
- If there is a knowledge gap, generate a follow-up query that would help expand your understanding.
- Focus on technical details, implementation specifics, or emerging trends that weren't fully covered.

Requirements:
- Ensure the follow-up query is self-contained and includes necessary context for web search.

Output Format:
- Format your response as a JSON object with these exact keys:
   - "is_sufficient": true or false
   - "knowledge_gap": Describe what information is missing or needs clarification
   - "follow_up_queries": Write a specific question to address this gap

Example:
```json
{{
    "is_sufficient": true, // or false
    "knowledge_gap": "The summary lacks information about performance metrics and benchmarks", // "" if is_sufficient is true
    "follow_up_queries": ["What are typical performance benchmarks and metrics used to evaluate [specific technology]?"] // [] if is_sufficient is true
}}
```

Reflect carefully on the Summaries to identify knowledge gaps and produce a follow-up query. Then, produce your output following this JSON format:

Summaries:
{summaries}
"""

answer_instructions = """Generate a high-quality answer to the user's question based on the provided summaries.

Instructions:
- The current date is {current_date}.
- You are the final step of a multi-step research process, don't mention that you are the final step.
- You have access to all the information gathered from the previous steps.
- You have access to the user's question.
- Generate a high-quality answer to the user's question based on the provided summaries and the user's question.
- Include the sources you used from the Summaries in the answer correctly, use markdown format (e.g. [apnews](https://vertexaisearch.cloud.google.com/id/1-0)). THIS IS A MUST.

User Context:
- {research_topic}

Summaries:
{summaries}"""


# Credit Rating Specific Prompts

credit_rating_query_generator_instructions = """You are a specialized credit rating research agent. Generate targeted search queries to find credit rating information for power plants using a two-tier search strategy.

**Power Plant Information:**
- Power Plant Name: {power_plant_name}
- Geographic Region: {geographic_region}
- Current Date: {current_date}

**Search Strategy:**

**PRIMARY STRATEGY (Execute First):**
Generate 4-5 targeted queries for direct power plant credit ratings with historical focus:
1. "[Power Plant Name] credit rating history 2019-2024"
2. "[Power Plant Name] financial rating historical data"
3. "[Power Plant Name] bond rating annual reports"
4. "[Power Plant Name] debt rating [Geographic Region] 2019 2020 2021 2022 2023 2024"
5. "[Power Plant Name] rating changes upgrades downgrades"

**FALLBACK STRATEGY (Only if primary yields insufficient results):**
Generate 4-5 queries to identify parent company and search their historical ratings:
1. "[Power Plant Name] holding company parent operator"
2. "[Power Plant Name] developer owner company"
3. "[Parent Company Name] credit rating history 2019-2024" (once identified)
4. "[Parent Company Name] bond rating historical [Geographic Region]"
5. "[Parent Company Name] annual rating reports financial history"

**Geographic Targeting:**
- **India**: Include "CRISIL", "ICRA", "CARE Ratings", "India Ratings"
- **United States**: Include "Moody's", "S&P", "Fitch"
- **United Kingdom**: Include "Fitch", "S&P", "Moody's"
- **Europe**: Include "Fitch", "S&P", "Moody's", "Scope Ratings"

**Time Range Focus:**
- **CRITICAL**: Search for ratings from ALL years 2019-2024 (5 years of data required)
- Include specific year terms: "2019", "2020", "2021", "2022", "2023", "2024"
- Search for historical rating changes and trends
- Look for rating history, rating evolution, and multi-year rating reports

**Output Format:**
Format your response as a JSON object with these exact keys:
- "primary_queries": List of direct power plant rating queries
- "fallback_queries": List of parent company identification and rating queries
- "rationale": Explanation of search strategy
- "power_plant_name": The power plant name being researched
- "geographic_region": Geographic region for targeted search

Example:
```json
{{
    "primary_queries": [
        "Amravati Thermal Power Project credit rating history 2019-2024",
        "Amravati Thermal Power Project financial rating CRISIL ICRA historical data",
        "Amravati Thermal Power Project bond rating India annual reports",
        "Amravati Thermal Power Project rating changes upgrades downgrades"
    ],
    "fallback_queries": [
        "Amravati Thermal Power Project holding company parent operator",
        "Amravati Thermal Power Project developer owner company",
        "NTPC credit rating history 2019-2024 India",
        "NTPC annual rating reports financial history"
    ],
    "rationale": "Primary strategy targets direct facility ratings with 5-year historical data from Indian agencies. Fallback strategy identifies NTPC as parent company for corporate rating history.",
    "power_plant_name": "Amravati Thermal Power Project",
    "geographic_region": "India"
}}
```

Research Topic: {research_topic}"""


credit_rating_web_searcher_instructions = """You are a specialized credit rating research agent. Conduct targeted searches to find comprehensive credit rating information for power plants.

**Search Objective:** Find credit rating information for "{power_plant_name}" in "{geographic_region}"

**Current Date:** {current_date}

**Search Instructions:**
1. **Execute searches systematically** - start with primary strategy queries
2. **Target specific rating agencies** based on geographic region:
   - **India**: CRISIL, ICRA, CARE Ratings, India Ratings & Research, Brickwork Ratings
   - **US**: Moody's, S&P Global Ratings, Fitch Ratings
   - **UK/Europe**: Fitch, S&P, Moody's, Scope Ratings
3. **CRITICAL - Search for COMPLETE 5-year history (2019-2024)**:
   - Look for rating history reports, annual rating reviews
   - Search for rating changes, upgrades, downgrades over time
   - Include terms: "rating history", "historical ratings", "rating trend", "rating evolution"
4. **Look for specific rating information FOR EACH YEAR**:
   - Full rating with outlook (e.g., "BBB+ Stable")
   - Rating dates and validity periods for each year
   - Instrument types (Long Term Debt, Facility Rating, etc.)
   - Currency context (INR, USD, GBP, EUR)
   - Rating rationale and changes year-over-year

**Data Collection Priority:**
1. **Direct power plant/facility ratings** (highest priority)
2. **Parent company ratings** (if direct ratings unavailable)
3. **Long-term debt ratings** (prioritize over short-term)
4. **COMPLETE 5-year rating history** (2019-2024) - NOT just most recent
5. **Rating changes and trends** - look for upgrades, downgrades, outlook changes

**Search Strategy:**
- If primary queries yield insufficient results, identify parent/holding company
- Search for parent company credit ratings as fallback
- Cross-reference multiple rating agencies for comprehensive coverage
- Verify rating authenticity and recency

**Output Requirements:**
- Synthesize findings into a comprehensive summary
- Clearly distinguish between direct facility ratings vs. parent company ratings
- Include source attribution for all rating information
- Note any gaps in coverage or missing information
- Specify currency context and geographic relevance

**Search Topic:** {search_query}

Focus on gathering verifiable credit rating data with proper source attribution."""


credit_rating_reflection_instructions = """You are analyzing credit rating research results for "{power_plant_name}" in "{geographic_region}".

**Analysis Objective:** Determine if sufficient credit rating information has been gathered and identify any gaps.

**Current Date:** {current_date}

**Evaluation Criteria:**

**SUFFICIENT INFORMATION includes:**
- At least one verified credit rating from a recognized agency
- **COMPLETE 5-year rating data (2019-2024)** - minimum 3 years required
- Clear indication whether rating is for power plant directly or parent company
- Proper currency context (INR/USD/GBP/EUR)
- Source attribution for rating information
- Rating history showing changes over time

**INSUFFICIENT INFORMATION gaps:**
- No credit ratings found from any recognized agencies
- Only outdated ratings (pre-2019)
- **Incomplete time coverage** - missing years from 2019-2024 range
- Only 1-2 years of data instead of 5-year history
- Unclear rating source or methodology
- Missing parent company identification (if direct ratings unavailable)
- Incomplete geographic coverage for major local rating agencies

**Follow-up Query Generation:**
If information is insufficient, generate 1-3 specific follow-up queries:

**For Missing Direct Ratings:**
- "[Power Plant Name] facility rating [Local Agency Names]"
- "[Power Plant Name] project finance rating [Year Range]"
- "[Power Plant Name] infrastructure rating [Geographic Region]"

**For Parent Company Search:**
- "[Power Plant Name] developer company owner"
- "[Power Plant Name] financing company sponsor"
- "[Identified Parent Company] credit rating [Local Agencies] [Year Range]"

**For Geographic Coverage:**
- "[Power Plant Name] [Specific Local Agency] rating"
- "[Power Plant Name] local currency rating [Currency Code]"

**Rating Completeness Assessment:**
Evaluate coverage across:
- Major local rating agencies for the region
- Time range coverage (2019-2024)
- Rating type coverage (facility vs. corporate)
- Currency and regulatory context

**Output Format:**
```json
{{
    "is_sufficient": true/false,
    "knowledge_gap": "Description of missing information",
    "follow_up_queries": ["Specific targeted queries"],
    "rating_completeness": "Assessment of coverage gaps",
    "suggested_strategy": "Recommended next steps"
}}
```

**Research Summaries to Analyze:**
{summaries}"""


credit_rating_answer_instructions = """Generate a comprehensive credit rating analysis based on the research findings.

**Analysis Objective:** Provide structured credit rating information for "{power_plant_name}"

**Current Date:** {current_date}

**Output Requirements:**

**1. Structured JSON Response:**
You must provide a valid JSON response in this exact format:

```json
{{
    "credit_rating": [
        {{
            "agency": "AGENCY_CODE",
            "name": "Full Agency Name",
            "yearwise_rating": [
                {{
                    "rating": "Full rating with outlook",
                    "rating_trunc": "Core rating only",
                    "year": "YYYY"
                }}
            ]
        }}
    ],
    "credit_rating_note": "Descriptive note about rating methodology and context",
    "currency": "Three-letter currency code",
    "level": "power_plant OR parent_company"
}}
```

**2. Data Processing Rules:**

**Rating Truncation:**
- Remove outlook indicators: "Stable", "Positive", "Negative", "Watch", "Under Review"
- **Important**: "BBB+ Stable" → rating_trunc: "BBB+"

**Agency Codes:**
- CRISIL, ICRA, CARE, INDIA_RATINGS, BRICKWORK (India)
- SP, MOODYS, FITCH (International)
- SCOPE (Europe)

**credit_rating_note:**
- Provide a concise summary listing each agency and the instrument type they rated
- Format: "[Agency Name]: [Instrument Type]. [Next Agency]: [Instrument Type]."
- Keep it brief - only agency name and instrument type (e.g., "Long-term bank facilities", "NCDs", "Corporate bonds", "Project finance rating")
- Do NOT include detailed analysis, rating history, or explanations

**Currency Assignment:**
- India: INR
- United States: USD
- United Kingdom: GBP
- Europe: EUR
- Determine from rating agency's operating region

**Level Classification:**
- "power_plant": Direct facility/project ratings
- "parent_company": Parent entity corporate ratings

**3. Data Validation:**
- **CRITICAL**: Include ratings from ALL available years 2019-2024 (aim for complete 5-year history)
- If a year is missing, explicitly note it in the credit_rating_note
- Prioritize Long Term Debt/Facility ratings
- Ensure all required fields are present
- Verify rating format consistency
- Show rating progression/changes over the years

**4. Concise Note:**
Include only:
- Each agency name and the specific instrument type they rated
- Format as simple list: "Agency1: Instrument Type. Agency2: Instrument Type."
- Examples: "CRISIL: Long-term bank facilities. ICRA: NCDs. Moody's: Corporate bonds."

**5. Source Attribution:**
Include markdown citations for all rating information using format: [source](url)

**Research Context:**
- Power Plant: {power_plant_name}
- Geographic Region: {geographic_region}

**Research Summaries:**
{summaries}

Provide the structured JSON response followed by any additional analysis or context."""
