import os
import re

from agent.tools_and_schemas import SearchQueryList, Reflection
from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from langgraph.types import Send
from langgraph.graph import StateGraph
from langgraph.graph import START, END
from langchain_core.runnables import RunnableConfig
from google.genai import Client

from agent.state import (
    OverallState,
    QueryGenerationState,
    ReflectionState,
    WebSearchState,
    CreditRatingQueryGenerationState,
    CreditRatingReflectionState,
)
from agent.configuration import Configuration
from agent.prompts import (
    get_current_date,
    query_writer_instructions,
    web_searcher_instructions,
    reflection_instructions,
    answer_instructions,
)
from agent.credit_rating_nodes import (
    generate_credit_rating_query,
    continue_to_credit_rating_web_research,
    credit_rating_web_research,
    credit_rating_reflection,
    finalize_credit_rating_answer,
)
from agent.graph import (
    generate_query,
    continue_to_web_research,
    web_research,
    reflection,
    evaluate_research,
    finalize_answer,
)
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from agent.utils import (
    get_citations,
    get_research_topic,
    insert_citation_markers,
    resolve_urls,
)

load_dotenv()

if os.getenv("GEMINI_API_KEY") is None:
    raise ValueError("GEMINI_API_KEY is not set")

# Used for Google Search API
genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))


def detect_credit_rating_request(state: OverallState) -> str:
    """Routing function to determine if this is a credit rating request."""
    research_topic = get_research_topic(state["messages"]).lower()
    
    # Keywords that indicate credit rating research
    credit_rating_keywords = [
        "credit rating", "financial rating", "bond rating", "debt rating",
        "rating agency", "moody's", "s&p", "fitch", "crisil", "icra", "care",
        "investment grade", "rating outlook", "credit assessment"
    ]
    
    power_plant_keywords = [
        "power plant", "thermal power", "nuclear power", "solar power",
        "wind power", "hydroelectric", "power project", "power station",
        "energy project", "electricity generation"
    ]
    
    # Check if both credit rating and power plant keywords are present
    has_credit_rating = any(keyword in research_topic for keyword in credit_rating_keywords)
    has_power_plant = any(keyword in research_topic for keyword in power_plant_keywords)
    
    if has_credit_rating and has_power_plant:
        return "credit_rating_query_generation"
    else:
        return "general_query_generation"


def evaluate_credit_rating_research(
    state: CreditRatingReflectionState,
    config: RunnableConfig,
) -> str:
    """LangGraph routing function for credit rating research flow.

    Controls the credit rating research loop by deciding whether to continue gathering
    information or to finalize the analysis.

    Args:
        state: Current graph state containing the research loop count
        config: Configuration for the runnable, including max_research_loops setting

    Returns:
        String literal indicating the next node to visit
    """
    configurable = Configuration.from_runnable_config(config)
    max_research_loops = (
        state.get("max_research_loops")
        if state.get("max_research_loops") is not None
        else configurable.max_research_loops
    )
    
    if state["is_sufficient"] or state["research_loop_count"] >= max_research_loops:
        return "finalize_credit_rating_answer"
    else:
        # Check if we should switch to fallback strategy
        if state.get("suggested_strategy") == "switch to parent company search":
            # Generate fallback queries for parent company search
            return [
                Send(
                    "credit_rating_web_research",
                    {
                        "search_query": follow_up_query,
                        "id": state["number_of_ran_queries"] + int(idx),
                    },
                )
                for idx, follow_up_query in enumerate(state["follow_up_queries"])
            ]
        else:
            # Continue with regular follow-up queries
            return [
                Send(
                    "credit_rating_web_research",
                    {
                        "search_query": follow_up_query,
                        "id": state["number_of_ran_queries"] + int(idx),
                    },
                )
                for idx, follow_up_query in enumerate(state["follow_up_queries"])
            ]


# Create our Enhanced Agent Graph with Credit Rating Support
builder = StateGraph(OverallState, config_schema=Configuration)

# Define the nodes we will cycle between
# General research nodes
builder.add_node("general_query_generation", generate_query)
builder.add_node("web_research", web_research)
builder.add_node("reflection", reflection)
builder.add_node("finalize_answer", finalize_answer)

# Credit rating specific nodes
builder.add_node("credit_rating_query_generation", generate_credit_rating_query)
builder.add_node("credit_rating_web_research", credit_rating_web_research)
builder.add_node("credit_rating_reflection", credit_rating_reflection)
builder.add_node("finalize_credit_rating_answer", finalize_credit_rating_answer)

# Set the entrypoint with routing logic
builder.add_conditional_edges(
    START,
    detect_credit_rating_request,
    ["general_query_generation", "credit_rating_query_generation"]
)

# General research flow
builder.add_conditional_edges(
    "general_query_generation", continue_to_web_research, ["web_research"]
)
builder.add_edge("web_research", "reflection")
builder.add_conditional_edges(
    "reflection", evaluate_research, ["web_research", "finalize_answer"]
)
builder.add_edge("finalize_answer", END)

# Credit rating research flow
builder.add_conditional_edges(
    "credit_rating_query_generation", 
    continue_to_credit_rating_web_research, 
    ["credit_rating_web_research"]
)
builder.add_edge("credit_rating_web_research", "credit_rating_reflection")
builder.add_conditional_edges(
    "credit_rating_reflection", 
    evaluate_credit_rating_research, 
    ["credit_rating_web_research", "finalize_credit_rating_answer"]
)
builder.add_edge("finalize_credit_rating_answer", END)

# Compile the enhanced graph
enhanced_graph = builder.compile(name="enhanced-pro-search-agent")
