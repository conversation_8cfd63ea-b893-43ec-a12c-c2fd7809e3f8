from typing import List, Optional, Literal
from pydantic import BaseModel, Field, field_validator
import re


class SearchQueryList(BaseModel):
    query: List[str] = Field(
        description="A list of search queries to be used for web research."
    )
    rationale: str = Field(
        description="A brief explanation of why these queries are relevant to the research topic."
    )


class Reflection(BaseModel):
    is_sufficient: bool = Field(
        description="Whether the provided summaries are sufficient to answer the user's question."
    )
    knowledge_gap: str = Field(
        description="A description of what information is missing or needs clarification."
    )
    follow_up_queries: List[str] = Field(
        description="A list of follow-up queries to address the knowledge gap."
    )


# Credit Rating Specific Schemas

class YearwiseRating(BaseModel):
    """Individual year rating data."""
    rating: str = Field(
        description="Full rating with outlook (e.g., 'BBB+ Stable', 'A- Negative')"
    )
    rating_trunc: str = Field(
        description="Core rating only without outlook (e.g., 'BBB+', 'A-')"
    )
    year: str = Field(
        description="Year in YYYY format"
    )

    @field_validator('year')
    @classmethod
    def validate_year(cls, v):
        if not re.match(r'^\d{4}$', v):
            raise ValueError('Year must be in YYYY format')
        year_int = int(v)
        if year_int < 2019 or year_int > 2024:
            raise ValueError('Year must be between 2019 and 2024')
        return v

    @field_validator('rating_trunc')
    @classmethod
    def validate_rating_trunc(cls, v, info):
        """Ensure rating_trunc is derived from rating by removing outlook indicators."""
        if info.data and 'rating' in info.data:
            full_rating = info.data['rating']

            # Handle special cases first
            special_cases = {
                'Issuer Not Cooperating': 'Not Rated',
                'Not Rated': 'Not Rated',
                'Withdrawn': 'Withdrawn',
                'Under Review': 'Under Review'
            }

            if full_rating in special_cases:
                expected = special_cases[full_rating]
                if v != expected:
                    raise ValueError(f'rating_trunc "{v}" does not match expected "{expected}" for special case "{full_rating}"')
                return v

            # Remove common outlook indicators and parenthetical information
            outlook_indicators = ['Stable', 'Positive', 'Negative', 'Watch', 'Under Review', 'Developing']
            parenthetical_indicators = ['Issuer Not Cooperating', 'Under Review', 'Watch']

            truncated = full_rating

            # Remove parenthetical content that contains outlook/status indicators
            import re
            for indicator in parenthetical_indicators:
                pattern = rf'\s*\({re.escape(indicator)}\)\s*'
                truncated = re.sub(pattern, '', truncated, flags=re.IGNORECASE)

            # Remove standalone outlook indicators
            for indicator in outlook_indicators:
                truncated = truncated.replace(indicator, '').strip()

            # Clean up extra spaces and parentheses
            truncated = re.sub(r'\s+', ' ', truncated).strip()
            truncated = re.sub(r'\(\s*\)', '', truncated).strip()

            # Validate that rating_trunc matches the truncated version
            if v != truncated:
                # Allow some flexibility for common variations
                if v.replace(' ', '') == truncated.replace(' ', ''):
                    return v  # Allow spacing differences
                raise ValueError(f'rating_trunc "{v}" does not match truncated rating "{truncated}" (from "{full_rating}")')
        return v


class CreditRatingAgency(BaseModel):
    """Credit rating agency information."""
    agency: str = Field(
        description="Agency code (e.g., 'CRISIL', 'SP', 'MOODYS', 'FITCH')"
    )
    name: str = Field(
        description="Full agency name (e.g., 'CRISIL Limited', 'S&P Global Ratings')"
    )
    yearwise_rating: List[YearwiseRating] = Field(
        description="List of ratings by year"
    )

    @field_validator('agency')
    @classmethod
    def validate_agency_code(cls, v):
        # Common agency codes
        valid_agencies = {
            'CRISIL', 'ICRA', 'CARE', 'INDIA_RATINGS', 'BRICKWORK',  # India
            'SP', 'MOODYS', 'FITCH',  # International
            'SCOPE'  # Europe
        }
        if v.upper() not in valid_agencies:
            # Allow other agencies but ensure uppercase
            return v.upper()
        return v.upper()


class CreditRatingResponse(BaseModel):
    """Complete credit rating response structure."""
    credit_rating: List[CreditRatingAgency] = Field(
        description="List of credit rating agencies and their ratings"
    )
    credit_rating_note: str = Field(
        description="Descriptive note about rating type, methodology, and context"
    )
    currency: str = Field(
        description="Three-letter currency code (INR/USD/GBP/EUR/etc.)"
    )
    level: Literal["power_plant", "parent_company"] = Field(
        description="Whether rating is for power plant directly or parent company"
    )

    @field_validator('currency')
    @classmethod
    def validate_currency(cls, v):
        # Common currency codes
        valid_currencies = {'INR', 'USD', 'GBP', 'EUR', 'JPY', 'CAD', 'AUD', 'CHF'}
        if v.upper() not in valid_currencies:
            raise ValueError(f'Currency code "{v}" not recognized. Use standard 3-letter codes.')
        return v.upper()


class CreditRatingQueryList(BaseModel):
    """Specialized query list for credit rating searches."""
    primary_queries: List[str] = Field(
        description="Primary search queries targeting direct power plant credit ratings"
    )
    fallback_queries: List[str] = Field(
        description="Fallback queries for parent company identification and rating search"
    )
    rationale: str = Field(
        description="Explanation of the search strategy and query selection"
    )
    power_plant_name: str = Field(
        description="Name of the power plant being researched"
    )
    geographic_region: Optional[str] = Field(
        description="Geographic region for targeted agency search (India, US, UK, Europe, etc.)"
    )


class CreditRatingReflection(BaseModel):
    """Reflection model for credit rating searches."""
    is_sufficient: bool = Field(
        description="Whether sufficient credit rating information has been found"
    )
    knowledge_gap: str = Field(
        description="Description of missing credit rating information"
    )
    follow_up_queries: List[str] = Field(
        description="Follow-up queries to find missing credit rating data"
    )
    rating_completeness: str = Field(
        description="Assessment of rating data completeness (agencies covered, time range, etc.)"
    )
    suggested_strategy: Optional[str] = Field(
        description="Suggested search strategy adjustment (e.g., 'switch to parent company search')"
    )
