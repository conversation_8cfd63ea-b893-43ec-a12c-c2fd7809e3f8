{"credit_rating": [{"agency": "CRISIL", "name": "CRISIL Ratings", "yearwise_rating": [{"rating": "CRISIL AA-/Positive", "rating_trunc": "CRISIL AA-", "year": "2024"}, {"rating": "CRISIL A/Stable", "rating_trunc": "CRISIL A", "year": "2023"}, {"rating": "CRISIL A/Stable", "rating_trunc": "CRISIL A", "year": "2022"}]}, {"agency": "INDIA_RATINGS", "name": "India Ratings & Research", "yearwise_rating": [{"rating": "IND AA/Stable", "rating_trunc": "IND AA", "year": "2024"}, {"rating": "IND A-/Positive", "rating_trunc": "IND A-", "year": "2022"}, {"rating": "IND A-/Positive", "rating_trunc": "IND A-", "year": "2021"}, {"rating": "IND BBB-/Stable", "rating_trunc": "IND BBB-", "year": "2020"}, {"rating": "IND BBB+/Stable", "rating_trunc": "IND BBB+", "year": "2019"}]}, {"agency": "ICRA", "name": "ICRA Limited", "yearwise_rating": [{"rating": "[ICRA]AA (Stable)", "rating_trunc": "[ICRA]AA", "year": "2024"}]}, {"agency": "CARE", "name": "CARE Ratings", "yearwise_rating": [{"rating": "CARE BBB+; Stable (Withdrawn)", "rating_trunc": "CARE BBB+", "year": "2023"}]}, {"agency": "BRICKWORK", "name": "Brickwork Ratings", "yearwise_rating": [{"rating": "Withdrawn", "rating_trunc": "Withdrawn", "year": "2023"}, {"rating": "BWR BBB- (Stable)/A3", "rating_trunc": "BWR BBB-/A3", "year": "2022"}]}], "credit_rating_note": "CRISIL: Long-term bank facilities. India Ratings & Research: Long-term bank facilities. ICRA: Term loan facilities. CARE Ratings: Bank facilities. Brickwork Ratings: Bank loan facilities.", "currency": "INR", "level": "parent_company", "_validation_error": "9 validation errors for CreditRatingResponse\ncredit_rating.0.yearwise_rating.0.rating_trunc\n  Value error, rating_trunc \"CRISIL AA-\" does not match truncated rating \"CRISIL AA-/\" (from \"CRISIL AA-/Positive\") [type=value_error, input_value='CRISIL AA-', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error\ncredit_rating.0.yearwise_rating.1.rating_trunc\n  Value error, rating_trunc \"CRISIL A\" does not match truncated rating \"CRISIL A/\" (from \"CRISIL A/Stable\") [type=value_error, input_value='CRISIL A', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error\ncredit_rating.0.yearwise_rating.2.rating_trunc\n  Value error, rating_trunc \"CRISIL A\" does not match truncated rating \"CRISIL A/\" (from \"CRISIL A/Stable\") [type=value_error, input_value='CRISIL A', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error\ncredit_rating.1.yearwise_rating.0.rating_trunc\n  Value error, rating_trunc \"IND AA\" does not match truncated rating \"IND AA/\" (from \"IND AA/Stable\") [type=value_error, input_value='IND AA', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error\ncredit_rating.1.yearwise_rating.1.rating_trunc\n  Value error, rating_trunc \"IND A-\" does not match truncated rating \"IND A-/\" (from \"IND A-/Positive\") [type=value_error, input_value='IND A-', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error\ncredit_rating.1.yearwise_rating.2.rating_trunc\n  Value error, rating_trunc \"IND A-\" does not match truncated rating \"IND A-/\" (from \"IND A-/Positive\") [type=value_error, input_value='IND A-', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error\ncredit_rating.1.yearwise_rating.3.rating_trunc\n  Value error, rating_trunc \"IND BBB-\" does not match truncated rating \"IND BBB-/\" (from \"IND BBB-/Stable\") [type=value_error, input_value='IND BBB-', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error\ncredit_rating.1.yearwise_rating.4.rating_trunc\n  Value error, rating_trunc \"IND BBB+\" does not match truncated rating \"IND BBB+/\" (from \"IND BBB+/Stable\") [type=value_error, input_value='IND BBB+', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error\ncredit_rating.3.yearwise_rating.0.rating_trunc\n  Value error, rating_trunc \"CARE BBB+\" does not match truncated rating \"CARE BBB+; (Withdrawn)\" (from \"CARE BBB+; Stable (Withdrawn)\") [type=value_error, input_value='CARE BBB+', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error", "_validation_status": "failed"}