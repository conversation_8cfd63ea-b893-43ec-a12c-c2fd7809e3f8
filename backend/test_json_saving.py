#!/usr/bin/env python3
"""
Test JSON saving functionality
"""

import sys
import os
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.tools_and_schemas import CreditRatingResponse


def test_json_saving():
    """Test that we can save the credit rating JSON correctly."""
    
    print("🧪 Testing JSON Saving Functionality")
    print("=" * 50)
    
    # Load the manually created JSON
    with open('amravati_rating.json', 'r') as f:
        credit_rating_data = json.load(f)
    
    print(f"📊 Loaded credit rating data:")
    print(f"   - Agencies: {len(credit_rating_data['credit_rating'])}")
    print(f"   - Currency: {credit_rating_data['currency']}")
    print(f"   - Level: {credit_rating_data['level']}")
    
    # Count total ratings
    total_ratings = 0
    for agency in credit_rating_data['credit_rating']:
        years = [rating['year'] for rating in agency['yearwise_rating']]
        total_ratings += len(years)
        print(f"   - {agency['name']}: {len(years)} years ({', '.join(years)})")
    
    print(f"   - Total ratings: {total_ratings}")
    
    # Test validation
    try:
        validated_data = CreditRatingResponse(**credit_rating_data)
        print(f"✅ Validation successful!")
        
        # Test saving to different file
        output_file = 'test_validated_output.json'
        with open(output_file, 'w') as f:
            json.dump(validated_data.model_dump(), f, indent=2)
        
        print(f"✅ Saved validated data to: {output_file}")
        
        # Check file size
        file_size = os.path.getsize(output_file)
        print(f"📁 File size: {file_size} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return False


def test_example_script_logic():
    """Test the logic used in the example script."""
    
    print(f"\n🔧 Testing Example Script Logic")
    print("=" * 40)
    
    # Simulate what the example script does
    credit_rating_result = {
        "credit_rating": [
            {
                "agency": "CRISIL",
                "name": "CRISIL Ratings",
                "yearwise_rating": [
                    {"rating": "BBB+ Stable", "rating_trunc": "BBB+", "year": "2024"}
                ]
            }
        ],
        "credit_rating_note": "Test note",
        "currency": "INR",
        "level": "power_plant"
    }
    
    # Test the saving logic
    save_file = 'test_example_logic.json'
    
    if credit_rating_result:
        try:
            credit_rating_json = json.dumps(credit_rating_result, indent=2)
            with open(save_file, 'w') as f:
                f.write(credit_rating_json)
            
            print(f"✅ Example script logic works - saved to: {save_file}")
            
            # Check if there were validation issues
            if credit_rating_result.get("_validation_status") == "failed":
                print(f"⚠️  Would show validation warnings")
                print(f"    Validation issue: {credit_rating_result.get('_validation_error', 'Unknown error')}")
            else:
                print(f"✅ No validation issues detected")
                
            return True
            
        except Exception as e:
            print(f"❌ Error in example script logic: {e}")
            return False
    else:
        print(f"❌ credit_rating_result is None or empty")
        return False


if __name__ == "__main__":
    print("🚀 Credit Rating JSON Saving Tests")
    print("=" * 60)
    
    success1 = test_json_saving()
    success2 = test_example_script_logic()
    
    if success1 and success2:
        print(f"\n🎉 All tests passed! JSON saving functionality is working correctly.")
        print(f"📁 Files created:")
        print(f"   - amravati_rating.json (manual example)")
        print(f"   - test_validated_output.json (validated)")
        print(f"   - test_example_logic.json (example script logic)")
    else:
        print(f"\n❌ Some tests failed. Check the implementation.")
