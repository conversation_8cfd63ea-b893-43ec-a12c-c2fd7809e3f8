#!/usr/bin/env python3
"""
Test JSON extraction and saving functionality
"""

import sys
import os
import json
import re

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.tools_and_schemas import CreditRatingResponse


def test_json_extraction():
    """Test JSON extraction from sample responses."""
    
    # Sample response that might come from the LLM
    sample_response = """
Based on my research, here is the credit rating information for the power plant:

```json
{
    "credit_rating": [
        {
            "agency": "CRISIL",
            "name": "CRISIL Limited",
            "yearwise_rating": [
                {
                    "rating": "BBB+ Stable",
                    "rating_trunc": "BBB+",
                    "year": "2024"
                },
                {
                    "rating": "BBB Positive",
                    "rating_trunc": "BBB",
                    "year": "2023"
                },
                {
                    "rating": "BBB- Stable",
                    "rating_trunc": "BBB-",
                    "year": "2022"
                },
                {
                    "rating": "BB+ Positive",
                    "rating_trunc": "BB+",
                    "year": "2021"
                },
                {
                    "rating": "BB Stable",
                    "rating_trunc": "BB",
                    "year": "2020"
                }
            ]
        },
        {
            "agency": "ICRA",
            "name": "ICRA Limited",
            "yearwise_rating": [
                {
                    "rating": "[ICRA]BBB+ (Stable)",
                    "rating_trunc": "[ICRA]BBB+",
                    "year": "2024"
                },
                {
                    "rating": "[ICRA]BBB (Positive)",
                    "rating_trunc": "[ICRA]BBB",
                    "year": "2023"
                }
            ]
        }
    ],
    "credit_rating_note": "Long-term facility ratings for thermal power generation project. CRISIL ratings show improvement from BB to BBB+ over 5 years. ICRA provides similar assessment with recent upgrades.",
    "currency": "INR",
    "level": "power_plant"
}
```

This shows a positive rating trend over the 5-year period.
"""

    print("🧪 Testing JSON Extraction")
    print("=" * 50)
    
    # Test the extraction patterns
    json_patterns = [
        r'```json\s*(\{.*?\})\s*```',  # Standard JSON code block
        r'```\s*(\{.*?\})\s*```',      # JSON without language specifier
        r'(\{[^{}]*"credit_rating"[^{}]*\{.*?\}[^{}]*\})',  # Direct JSON pattern
    ]
    
    credit_rating_data = None
    for i, pattern in enumerate(json_patterns, 1):
        print(f"\n📋 Testing pattern {i}: {pattern[:30]}...")
        json_match = re.search(pattern, sample_response, re.DOTALL)
        if json_match:
            try:
                credit_rating_data = json.loads(json_match.group(1))
                print(f"✅ Pattern {i} successfully extracted JSON")
                break
            except json.JSONDecodeError as e:
                print(f"❌ Pattern {i} found match but JSON decode failed: {e}")
                continue
        else:
            print(f"❌ Pattern {i} found no match")
    
    if not credit_rating_data:
        print("❌ No JSON could be extracted")
        return False
    
    print(f"\n📊 Extracted JSON Data:")
    print(f"   - Agencies: {len(credit_rating_data.get('credit_rating', []))}")
    print(f"   - Currency: {credit_rating_data.get('currency')}")
    print(f"   - Level: {credit_rating_data.get('level')}")
    
    # Test validation
    print(f"\n🔍 Testing Pydantic Validation...")
    try:
        validated_data = CreditRatingResponse(**credit_rating_data)
        print(f"✅ Validation successful!")
        
        # Check 5-year data coverage
        total_ratings = 0
        for agency in validated_data.credit_rating:
            years = [rating.year for rating in agency.yearwise_rating]
            total_ratings += len(years)
            print(f"   - {agency.name}: {len(years)} years ({', '.join(years)})")
        
        print(f"   - Total ratings across all agencies: {total_ratings}")
        
        if total_ratings >= 5:
            print(f"✅ Good coverage: {total_ratings} ratings found")
        else:
            print(f"⚠️  Limited coverage: Only {total_ratings} ratings found (aim for 5+ years)")
        
        # Test JSON serialization
        serialized = validated_data.model_dump()
        print(f"\n💾 Testing JSON Serialization...")
        json_str = json.dumps(serialized, indent=2)
        print(f"✅ Serialization successful ({len(json_str)} characters)")
        
        # Save to file for testing
        with open('test_output.json', 'w') as f:
            f.write(json_str)
        print(f"✅ Saved to test_output.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return False


def test_edge_cases():
    """Test edge cases for JSON extraction."""
    print(f"\n🔬 Testing Edge Cases")
    print("=" * 30)
    
    # Test case 1: JSON without code block
    response_no_block = """
    Here's the data: {"credit_rating": [{"agency": "TEST", "name": "Test Agency", "yearwise_rating": [{"rating": "A+ Stable", "rating_trunc": "A+", "year": "2024"}]}], "credit_rating_note": "Test note", "currency": "USD", "level": "power_plant"}
    """
    
    # Test case 2: Multiple JSON blocks
    response_multiple = """
    First block:
    ```json
    {"invalid": "data"}
    ```
    
    Correct block:
    ```json
    {"credit_rating": [{"agency": "TEST", "name": "Test Agency", "yearwise_rating": [{"rating": "A+ Stable", "rating_trunc": "A+", "year": "2024"}]}], "credit_rating_note": "Test note", "currency": "USD", "level": "power_plant"}
    ```
    """
    
    test_cases = [
        ("No code block", response_no_block),
        ("Multiple blocks", response_multiple)
    ]
    
    for name, response in test_cases:
        print(f"\n📝 Testing: {name}")
        
        json_patterns = [
            r'```json\s*(\{.*?\})\s*```',
            r'```\s*(\{.*?\})\s*```',
            r'(\{[^{}]*"credit_rating"[^{}]*\{.*?\}[^{}]*\})',
        ]
        
        found = False
        for pattern in json_patterns:
            json_match = re.search(pattern, response, re.DOTALL)
            if json_match:
                try:
                    data = json.loads(json_match.group(1))
                    if "credit_rating" in data:
                        print(f"✅ {name}: Successfully extracted valid credit rating JSON")
                        found = True
                        break
                except json.JSONDecodeError:
                    continue
        
        if not found:
            print(f"❌ {name}: Failed to extract valid JSON")


if __name__ == "__main__":
    print("🚀 Credit Rating JSON Extraction Tests")
    print("=" * 60)
    
    success = test_json_extraction()
    test_edge_cases()
    
    if success:
        print(f"\n🎉 All tests passed! JSON extraction and saving is working correctly.")
        print(f"📁 Check test_output.json for the saved result.")
    else:
        print(f"\n❌ Some tests failed. Check the implementation.")
