#!/usr/bin/env python3
"""
Test the validation fix for rating_trunc
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.tools_and_schemas import YearwiseRating, CreditRatingResponse


def test_validation_fix():
    """Test that the validation fix works for problematic ratings."""
    
    print("🧪 Testing Validation Fix")
    print("=" * 40)
    
    # Test cases that were failing before
    test_cases = [
        {
            "rating": "CRISIL AA-/Positive",
            "rating_trunc": "CRISIL AA-",
            "year": "2024"
        },
        {
            "rating": "CRISIL A/Stable", 
            "rating_trunc": "CRISIL A",
            "year": "2023"
        },
        {
            "rating": "IND AA/Stable",
            "rating_trunc": "IND AA",
            "year": "2024"
        },
        {
            "rating": "IND A-/Positive",
            "rating_trunc": "IND A-",
            "year": "2022"
        },
        {
            "rating": "CARE BBB+; Stable (Withdrawn)",
            "rating_trunc": "CARE BBB+",
            "year": "2023"
        },
        {
            "rating": "BWR BBB- (Stable)/A3",
            "rating_trunc": "BWR BBB-/A3",
            "year": "2022"
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            rating = YearwiseRating(**test_case)
            print(f"✅ Test {i}: {test_case['rating']} -> {test_case['rating_trunc']}")
            success_count += 1
        except Exception as e:
            print(f"❌ Test {i}: {test_case['rating']} -> {test_case['rating_trunc']}")
            print(f"   Error: {e}")
    
    print(f"\n📊 Results: {success_count}/{len(test_cases)} tests passed")
    
    if success_count == len(test_cases):
        print("🎉 All validation tests passed!")
        return True
    else:
        print("❌ Some validation tests failed.")
        return False


def test_full_credit_rating():
    """Test a full credit rating response that was failing before."""
    
    print(f"\n🧪 Testing Full Credit Rating Response")
    print("=" * 50)
    
    # Sample data that was failing validation
    credit_rating_data = {
        "credit_rating": [
            {
                "agency": "CRISIL",
                "name": "CRISIL Ratings",
                "yearwise_rating": [
                    {
                        "rating": "CRISIL AA-/Positive",
                        "rating_trunc": "CRISIL AA-",
                        "year": "2024"
                    },
                    {
                        "rating": "CRISIL A/Stable",
                        "rating_trunc": "CRISIL A",
                        "year": "2023"
                    }
                ]
            },
            {
                "agency": "INDIA_RATINGS",
                "name": "India Ratings & Research",
                "yearwise_rating": [
                    {
                        "rating": "IND AA/Stable",
                        "rating_trunc": "IND AA",
                        "year": "2024"
                    },
                    {
                        "rating": "IND A-/Positive",
                        "rating_trunc": "IND A-",
                        "year": "2022"
                    }
                ]
            }
        ],
        "credit_rating_note": "CRISIL: Long-term bank facilities. India Ratings & Research: Long-term bank facilities.",
        "currency": "INR",
        "level": "parent_company"
    }
    
    try:
        validated_data = CreditRatingResponse(**credit_rating_data)
        print("✅ Full credit rating validation successful!")
        print(f"   - Agencies: {len(validated_data.credit_rating)}")
        print(f"   - Total ratings: {sum(len(agency.yearwise_rating) for agency in validated_data.credit_rating)}")
        return True
    except Exception as e:
        print(f"❌ Full credit rating validation failed: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Testing Validation Fixes")
    print("=" * 60)
    
    success1 = test_validation_fix()
    success2 = test_full_credit_rating()
    
    if success1 and success2:
        print(f"\n🎉 All tests passed! Validation fix is working correctly.")
    else:
        print(f"\n❌ Some tests failed. Check the validation logic.")
