#!/usr/bin/env python3
"""
Credit Rating Research Example

This example demonstrates how to use the enhanced agent system for power plant credit rating research.
The system automatically detects credit rating requests and uses specialized search strategies.
"""

import argparse
import json
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src'))

from langchain_core.messages import HumanMessage
from agent.credit_rating_graph import enhanced_graph


def main() -> None:
    """Run the credit rating research agent from the command line."""
    parser = argparse.ArgumentParser(description="Run the Credit Rating Research Agent")
    parser.add_argument("question", help="Credit rating research question")
    parser.add_argument(
        "--initial-queries",
        type=int,
        default=3,
        help="Number of initial search queries",
    )
    parser.add_argument(
        "--max-loops",
        type=int,
        default=2,
        help="Maximum number of research loops",
    )
    parser.add_argument(
        "--reasoning-model",
        default="gemini-2.5-pro",
        help="Model for the final answer",
    )
    parser.add_argument(
        "--output-format",
        choices=["text", "json"],
        default="text",
        help="Output format (text or json)",
    )
    args = parser.parse_args()

    # Prepare the state
    state = {
        "messages": [HumanMessage(content=args.question)],
        "initial_search_query_count": args.initial_queries,
        "max_research_loops": args.max_loops,
        "reasoning_model": args.reasoning_model,
    }

    print(f"🔍 Researching: {args.question}")
    print("=" * 60)

    try:
        # Execute the enhanced graph
        result = enhanced_graph.invoke(state)
        
        # Extract results
        messages = result.get("messages", [])
        credit_rating_result = result.get("credit_rating_result")
        sources = result.get("sources_gathered", [])
        
        if args.output_format == "json":
            # JSON output format
            output = {
                "question": args.question,
                "answer": messages[-1].content if messages else "No answer generated",
                "credit_rating_data": credit_rating_result,
                "sources": sources,
                "metadata": {
                    "power_plant_name": result.get("power_plant_name"),
                    "geographic_region": result.get("geographic_region"),
                    "search_mode": result.get("search_mode"),
                    "research_loops": result.get("research_loop_count", 0)
                }
            }
            print(json.dumps(output, indent=2))
        else:
            # Text output format
            if messages:
                print("📋 Research Results:")
                print("-" * 40)
                print(messages[-1].content)
                print()
            
            if credit_rating_result:
                print("💰 Structured Credit Rating Data:")
                print("-" * 40)
                print(json.dumps(credit_rating_result, indent=2))
                print()
            
            if sources:
                print("📚 Sources:")
                print("-" * 40)
                for i, source in enumerate(sources, 1):
                    print(f"{i}. {source.get('label', 'Unknown')}: {source.get('value', 'No URL')}")
                print()
            
            # Print metadata
            print("ℹ️  Research Metadata:")
            print("-" * 40)
            print(f"Power Plant: {result.get('power_plant_name', 'Unknown')}")
            print(f"Geographic Region: {result.get('geographic_region', 'Unknown')}")
            print(f"Search Mode: {result.get('search_mode', 'general')}")
            print(f"Research Loops: {result.get('research_loop_count', 0)}")

    except Exception as e:
        print(f"❌ Error during research: {str(e)}")
        return 1

    return 0


def run_example_queries():
    """Run predefined example queries to demonstrate functionality."""
    
    example_queries = [
        "What is the credit rating of Amravati Thermal Power Project in India?",
        "Find credit rating information for Hinkley Point C nuclear power plant in UK",
        "Credit rating and financial assessment of Ivanpah Solar Power Facility in United States",
        "Bond rating and credit assessment of Hornsea One offshore wind farm in United Kingdom",
        "CRISIL and ICRA credit rating for Mundra Ultra Mega Power Project India"
    ]
    
    print("🚀 Running Credit Rating Research Examples")
    print("=" * 60)
    
    for i, query in enumerate(example_queries, 1):
        print(f"\n📝 Example {i}: {query}")
        print("-" * 60)
        
        state = {
            "messages": [HumanMessage(content=query)],
            "initial_search_query_count": 3,
            "max_research_loops": 1,  # Reduced for examples
            "reasoning_model": "gemini-2.5-flash",
        }
        
        try:
            result = enhanced_graph.invoke(state)
            
            # Print key results
            messages = result.get("messages", [])
            if messages:
                # Print first 200 characters of the answer
                answer = messages[-1].content
                print(f"Answer: {answer[:200]}{'...' if len(answer) > 200 else ''}")
            
            credit_rating_result = result.get("credit_rating_result")
            if credit_rating_result and not credit_rating_result.get("error"):
                print(f"✅ Structured credit rating data extracted successfully")
                if "credit_rating" in credit_rating_result:
                    agencies = len(credit_rating_result["credit_rating"])
                    print(f"📊 Found ratings from {agencies} agencies")
            else:
                print("⚠️  No structured credit rating data found")
            
            print(f"🏭 Power Plant: {result.get('power_plant_name', 'Unknown')}")
            print(f"🌍 Region: {result.get('geographic_region', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        print()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) == 1:
        # No arguments provided, run examples
        run_example_queries()
    else:
        # Arguments provided, run main function
        sys.exit(main())
