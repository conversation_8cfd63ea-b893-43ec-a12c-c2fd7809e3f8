#!/usr/bin/env python3
"""
Test Credit Rating Schemas

This script tests the credit rating data schemas and validation rules.
"""

import json
from pydantic import ValidationError
from agent.tools_and_schemas import (
    YearwiseRating,
    CreditRatingAgency,
    CreditRatingResponse,
    CreditRatingQueryList,
    CreditRatingReflection
)


def test_yearwise_rating():
    """Test YearwiseRating schema validation."""
    print("Testing YearwiseRating schema...")
    
    # Valid data
    valid_data = {
        "rating": "BBB+ Stable",
        "rating_trunc": "BBB+",
        "year": "2023"
    }
    
    try:
        rating = YearwiseRating(**valid_data)
        print(f"✅ Valid rating created: {rating}")
    except ValidationError as e:
        print(f"❌ Unexpected validation error: {e}")
        return False
    
    # Test invalid year
    try:
        invalid_year = YearwiseRating(rating="A+ Stable", rating_trunc="A+", year="2025")
        print(f"❌ Should have failed for year 2025")
        return False
    except ValidationError:
        print("✅ Correctly rejected invalid year (2025)")
    
    # Test rating truncation validation
    try:
        invalid_trunc = YearwiseRating(rating="BBB+ Stable", rating_trunc="A+", year="2023")
        print(f"❌ Should have failed for mismatched rating_trunc")
        return False
    except ValidationError:
        print("✅ Correctly rejected mismatched rating_trunc")
    
    return True


def test_credit_rating_agency():
    """Test CreditRatingAgency schema validation."""
    print("\nTesting CreditRatingAgency schema...")
    
    valid_data = {
        "agency": "CRISIL",
        "name": "CRISIL Limited",
        "yearwise_rating": [
            {
                "rating": "BBB+ Stable",
                "rating_trunc": "BBB+",
                "year": "2023"
            },
            {
                "rating": "BBB Positive",
                "rating_trunc": "BBB",
                "year": "2022"
            }
        ]
    }
    
    try:
        agency = CreditRatingAgency(**valid_data)
        print(f"✅ Valid agency created: {agency.agency} with {len(agency.yearwise_rating)} ratings")
        return True
    except ValidationError as e:
        print(f"❌ Validation error: {e}")
        return False


def test_credit_rating_response():
    """Test CreditRatingResponse schema validation."""
    print("\nTesting CreditRatingResponse schema...")
    
    valid_data = {
        "credit_rating": [
            {
                "agency": "CRISIL",
                "name": "CRISIL Limited",
                "yearwise_rating": [
                    {
                        "rating": "BBB+ Stable",
                        "rating_trunc": "BBB+",
                        "year": "2023"
                    }
                ]
            }
        ],
        "credit_rating_note": "Long-term facility rating for power generation project",
        "currency": "INR",
        "level": "power_plant"
    }
    
    try:
        response = CreditRatingResponse(**valid_data)
        print(f"✅ Valid response created: {response.level} level, {response.currency} currency")
    except ValidationError as e:
        print(f"❌ Validation error: {e}")
        return False
    
    # Test invalid currency
    try:
        invalid_currency = valid_data.copy()
        invalid_currency["currency"] = "XYZ"
        response = CreditRatingResponse(**invalid_currency)
        print(f"❌ Should have failed for invalid currency")
        return False
    except ValidationError:
        print("✅ Correctly rejected invalid currency")
    
    # Test invalid level
    try:
        invalid_level = valid_data.copy()
        invalid_level["level"] = "invalid_level"
        response = CreditRatingResponse(**invalid_level)
        print(f"❌ Should have failed for invalid level")
        return False
    except ValidationError:
        print("✅ Correctly rejected invalid level")
    
    return True


def test_credit_rating_query_list():
    """Test CreditRatingQueryList schema validation."""
    print("\nTesting CreditRatingQueryList schema...")
    
    valid_data = {
        "primary_queries": [
            "Amravati Thermal Power Project credit rating",
            "Amravati Thermal Power Project CRISIL rating"
        ],
        "fallback_queries": [
            "Amravati Thermal Power Project parent company",
            "NTPC credit rating India"
        ],
        "rationale": "Primary strategy targets direct facility ratings",
        "power_plant_name": "Amravati Thermal Power Project",
        "geographic_region": "India"
    }
    
    try:
        query_list = CreditRatingQueryList(**valid_data)
        print(f"✅ Valid query list created: {len(query_list.primary_queries)} primary, {len(query_list.fallback_queries)} fallback")
        return True
    except ValidationError as e:
        print(f"❌ Validation error: {e}")
        return False


def test_sample_json_data():
    """Test with sample JSON data that might be returned by the system."""
    print("\nTesting with sample JSON data...")
    
    sample_json = """
    {
        "credit_rating": [
            {
                "agency": "CRISIL",
                "name": "CRISIL Limited",
                "yearwise_rating": [
                    {
                        "rating": "BBB+ Stable",
                        "rating_trunc": "BBB+",
                        "year": "2023"
                    },
                    {
                        "rating": "BBB Positive",
                        "rating_trunc": "BBB",
                        "year": "2022"
                    }
                ]
            },
            {
                "agency": "ICRA",
                "name": "ICRA Limited",
                "yearwise_rating": [
                    {
                        "rating": "[ICRA]BBB+ (Stable)",
                        "rating_trunc": "[ICRA]BBB+",
                        "year": "2023"
                    }
                ]
            }
        ],
        "credit_rating_note": "Long-term facility ratings for thermal power generation project. Ratings reflect adequate financial profile and operational track record.",
        "currency": "INR",
        "level": "power_plant"
    }
    """
    
    try:
        data = json.loads(sample_json)
        response = CreditRatingResponse(**data)
        print(f"✅ Sample JSON validated successfully")
        print(f"   - {len(response.credit_rating)} agencies")
        print(f"   - Currency: {response.currency}")
        print(f"   - Level: {response.level}")
        
        # Print ratings summary
        for agency in response.credit_rating:
            print(f"   - {agency.name}: {len(agency.yearwise_rating)} ratings")
        
        return True
    except (json.JSONDecodeError, ValidationError) as e:
        print(f"❌ Error with sample JSON: {e}")
        return False


def main():
    """Run all schema tests."""
    print("🧪 Credit Rating Schema Validation Tests")
    print("=" * 50)
    
    tests = [
        test_yearwise_rating,
        test_credit_rating_agency,
        test_credit_rating_response,
        test_credit_rating_query_list,
        test_sample_json_data
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
