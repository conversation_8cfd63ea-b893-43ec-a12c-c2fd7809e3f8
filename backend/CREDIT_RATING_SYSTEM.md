# Credit Rating Search Engine System

## Overview

This document describes the dedicated credit rating search engine system integrated with the existing agent architecture. The system automatically detects credit rating requests for power plants and uses specialized search strategies to gather comprehensive rating information.

## Architecture

### Core Components

1. **Enhanced Graph (`credit_rating_graph.py`)**: Main orchestration with automatic request type detection
2. **Credit Rating Nodes (`credit_rating_nodes.py`)**: Specialized agents for credit rating research
3. **Data Schemas (`tools_and_schemas.py`)**: Pydantic models for validation and structured output
4. **Specialized Prompts (`prompts.py`)**: Credit rating specific instructions and templates
5. **State Management (`state.py`)**: Extended state handling for credit rating workflows

### Two-Tier Search Strategy

#### Primary Strategy (Execute First)
- Direct power plant credit rating search
- Targeted queries: `"[Power Plant Name] credit rating"`, `"[Power Plant Name] financial rating"`
- Geographic agency targeting based on region

#### Fallback Strategy (If Primary Insufficient)
- Parent/holding company identification
- Corporate credit rating search for parent entities
- Queries: `"[Power Plant Name] holding company"`, `"[Parent Company] credit rating"`

### Geographic Coverage & Rating Agencies

- **India**: CRISIL, ICRA, CARE Ratings, India Ratings & Research, Brickwork Ratings
- **United States**: Moody's, S&P Global Ratings, Fitch Ratings
- **United Kingdom**: Fitch Ratings, S&P Global Ratings, Moody's
- **Europe**: Fitch Ratings, S&P Global Ratings, Moody's, Scope Ratings

## Data Processing

### Time Range
- **Focus**: 2019-2024 (configurable)
- **Priority**: Most recent ratings with historical context

### Rating Types
- **Preferred**: Long Term Debt/Facility ratings
- **Secondary**: Corporate ratings (for parent companies)
- **Currency Context**: INR, USD, GBP, EUR based on region

### Validation Rules
- **rating_trunc**: Strips outlook indicators (Stable, Positive, Negative, Watch, Under Review)
- **level**: Distinguishes "power_plant" vs "parent_company" ratings
- **currency**: Validates against standard 3-letter codes
- **year**: Enforces 2019-2024 range

## JSON Output Format

```json
{
  "credit_rating": [
    {
      "agency": "AGENCY_CODE",
      "name": "Full Agency Name",
      "yearwise_rating": [
        {
          "rating": "Full rating with outlook",
          "rating_trunc": "Core rating only",
          "year": "YYYY"
        }
      ]
    }
  ],
  "credit_rating_note": "Descriptive note about rating type and context",
  "currency": "Three-letter currency code",
  "level": "power_plant OR parent_company"
}
```

## Usage Examples

### Basic Usage

```python
from agent.credit_rating_graph import enhanced_graph
from langchain_core.messages import HumanMessage

# Credit rating request
state = {
    "messages": [HumanMessage(content="What is the credit rating of Amravati Thermal Power Project in India?")],
    "max_research_loops": 2
}

result = enhanced_graph.invoke(state)
```

### Command Line Usage

```bash
# Run credit rating research
python examples/credit_rating_research.py "Credit rating of Hinkley Point C nuclear power plant UK"

# JSON output format
python examples/credit_rating_research.py "CRISIL rating for Mundra Power Project India" --output-format json

# Run example queries
python examples/credit_rating_research.py
```

## Configuration

### Environment Variables
- `GEMINI_API_KEY`: Required for Google Search API and LLM access

### Configuration Parameters
```python
enable_credit_rating_mode: bool = True  # Auto-detect credit rating requests
credit_rating_time_range_start: int = 2019  # Start year for data collection
credit_rating_time_range_end: int = 2024    # End year for data collection
credit_rating_primary_query_count: int = 4  # Number of primary queries
credit_rating_fallback_query_count: int = 4 # Number of fallback queries
```

## Testing

### Schema Validation Tests
```bash
cd backend
PYTHONPATH=src python3 test_credit_rating_schemas.py
```

### Example Queries
```bash
cd backend
PYTHONPATH=src python3 examples/credit_rating_research.py
```

## Error Handling

### Comprehensive Logging
- Request type detection
- Query generation process
- Search execution status
- Data extraction results

### Graceful Degradation
- Fallback to general search if credit rating detection fails
- Default queries if LLM query generation fails
- Error messages in structured output format

### Validation
- Pydantic schema validation for all data structures
- Currency code validation
- Year range enforcement
- Rating format consistency checks

## Integration Points

### Automatic Detection
The system automatically detects credit rating requests by analyzing:
- Credit rating keywords: "credit rating", "financial rating", "bond rating"
- Power plant keywords: "power plant", "thermal power", "nuclear power"
- Rating agency names: "Moody's", "S&P", "CRISIL", "ICRA"

### Seamless Fallback
If not a credit rating request, the system falls back to the original general research workflow.

### State Management
Extended state tracking includes:
- `search_mode`: "general" or "credit_rating"
- `power_plant_name`: Extracted plant name
- `geographic_region`: Detected region
- `credit_rating_strategy`: "primary" or "fallback"
- `credit_rating_result`: Structured output data

## Performance Considerations

### Query Optimization
- Geographic targeting reduces irrelevant results
- Agency-specific queries improve precision
- Time range filtering focuses on recent data

### Token Efficiency
- URL shortening for citation management
- Structured output reduces parsing overhead
- Targeted prompts minimize unnecessary content

### Research Loop Control
- Configurable maximum research loops
- Intelligent reflection to determine sufficiency
- Strategy switching (primary to fallback) based on results

## Future Enhancements

### Potential Improvements
1. **Enhanced NLP**: Better power plant name and region extraction
2. **Agency Prioritization**: Dynamic agency selection based on region and plant type
3. **Historical Analysis**: Trend analysis across rating periods
4. **Multi-language Support**: Support for non-English rating documents
5. **Real-time Updates**: Integration with rating agency APIs for live data

### Extensibility
The modular design allows for easy extension to:
- Additional geographic regions
- New rating agencies
- Different asset types (beyond power plants)
- Custom validation rules
- Alternative output formats

## Quick Start Integration Guide

### 1. Replace Default Graph
To use the enhanced credit rating system, replace the default graph import:

```python
# Before
from agent.graph import graph

# After
from agent.credit_rating_graph import enhanced_graph as graph
```

### 2. Update Existing Applications
For existing applications using the original graph:

```python
# Minimal change required
from agent.credit_rating_graph import enhanced_graph

# Your existing code works unchanged
result = enhanced_graph.invoke(state)

# Access credit rating specific results
if result.get("credit_rating_result"):
    structured_data = result["credit_rating_result"]
```

### 3. Environment Setup
Ensure required environment variables:

```bash
export GEMINI_API_KEY="your-gemini-api-key"
```

### 4. Dependencies
No additional dependencies required - uses existing project dependencies.

### 5. Testing Integration
```bash
# Test schema validation
PYTHONPATH=src python3 test_credit_rating_schemas.py

# Test with example queries
PYTHONPATH=src python3 examples/credit_rating_research.py

# Test specific query
PYTHONPATH=src python3 examples/credit_rating_research.py "Credit rating of your power plant"
```

## File Structure

```
backend/src/agent/
├── credit_rating_graph.py      # Enhanced graph with credit rating support
├── credit_rating_nodes.py      # Credit rating specific nodes
├── tools_and_schemas.py        # Extended with credit rating schemas
├── prompts.py                  # Added credit rating prompts
├── state.py                    # Extended state management
├── configuration.py            # Added credit rating config
├── graph.py                    # Original graph (unchanged)
└── ...

backend/examples/
├── credit_rating_research.py   # Credit rating examples
└── cli_research.py            # Original CLI (unchanged)

backend/
├── test_credit_rating_schemas.py  # Schema validation tests
└── CREDIT_RATING_SYSTEM.md       # This documentation
```
